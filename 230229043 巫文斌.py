import cv2
from cvzone.FaceDetectionModule import FaceDetector

# 初始化摄像头和人脸检测器
# 使用cv2.VideoCapture来捕获视频帧，这里传入的是视频文件"dididi.mp4"的路径
cap = cv2.VideoCapture("dididi.mp4")
# 初始化人脸检测器，用于在视频帧中检测人脸
detector = FaceDetector()

# 创建滑动条窗口，用于调节Canny边缘检测的参数
# cv2.namedWindow用于创建一个指定名称的窗口
cv2.namedWindow("TrackBars")
# cv2.resizeWindow用于调整窗口的大小，这里将窗口大小设置为300x100像素
cv2.resizeWindow("TrackBars", 960, 90)

# 创建第一个滑动条，用于调节Canny边缘检测的阈值1
# cv2.createTrackbar用于创建一个滑动条，参数依次为滑动条名称、所属窗口名称、初始值、最大值和回调函数
# 这里的回调函数使用lambda _: None，表示不做任何操作
cv2.createTrackbar("Threshold1", "TrackBars", 50, 255, lambda _: None)
# 创建第二个滑动条，用于调节Canny边缘检测的阈值2
cv2.createTrackbar("Threshold2", "TrackBars", 150, 255, lambda _: None)

# 初始化模式变量，用于控制不同的人脸处理模式
# 0表示普通模式，为默认模式
mode = 0

# 进入无限循环，不断读取视频帧并进行处理
while True:
    # 读取视频帧
    # cap.read()返回两个值，success表示是否成功读取到帧，frame为读取到的视频帧
    success, frame = cap.read()
    # 如果没有成功读取到帧，说明视频结束，跳出循环
    if not success:
        break

    # 调整帧的大小为960x540像素
    # cv2.resize用于调整图像的大小，参数依次为要调整的图像、目标大小
    frame = cv2.resize(frame, (960, 540))

    # 进行人脸检测，并在检测到的人脸周围绘制边框
    # detector.findFaces返回两个值，处理后的帧和检测到的人脸信息列表
    frame, faces = detector.findFaces(frame, draw=True)

    # 如果检测到了人脸
    if faces:
        # 取第一个检测到的人脸的边界框信息
        # 边界框信息包含左上角坐标(x, y)和宽度w、高度h
        x, y, w, h = faces[0]['bbox']
        # 从原始帧中提取出人脸所在的区域，即感兴趣区域（ROI）
        face_area = frame[y:y + h, x:x + w]

        # 模式1：普通检测模式，按下按键1激活
        # 在这种模式下，不对人脸区域做任何处理
        if mode == 1:
            pass

        # 模式2：马赛克模式，按下按键2激活
        # 通过先缩小再放大的方式制造像素化效果
        elif mode == 2:
            # 缩放系数，值越小，马赛克效果越明显
            scale = 10
            # 先将人脸区域缩小
            small_face = cv2.resize(face_area, (w // scale, h // scale))
            # 再将缩小后的人脸区域放大到原来的大小，并替换原始帧中的人脸区域
            frame[y:y + h, x:x + w] = cv2.resize(small_face, (w, h))

        # 模式3：边缘检测模式，按下按键3激活
        # 结合滑动条的参数进行Canny边缘检测
        elif mode == 3:
            # 获取滑动条当前的值，作为Canny边缘检测的阈值
            thresh1 = cv2.getTrackbarPos("Threshold1", "TrackBars")
            thresh2 = cv2.getTrackbarPos("Threshold2", "TrackBars")
            # 将人脸区域转换为灰度图像
            gray_face = cv2.cvtColor(face_area, cv2.COLOR_BGR2GRAY)
            # 使用Canny算法进行边缘检测
            edges = cv2.Canny(gray_face, thresh1, thresh2)
            # 将边缘检测结果从单通道的灰度图像转换为三通道的BGR图像，以便与原始帧的通道数匹配
            frame[y:y + h, x:x + w] = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

        # 模式4：HSV色彩空间变换模式，按下按键4激活
        # 将人脸区域从BGR色彩空间转换为HSV色彩空间
        elif mode == 4:
            frame[y:y + h, x:x + w] = cv2.cvtColor(face_area, cv2.COLOR_BGR2HSV)

    # 在帧的左上角显示当前的处理模式提示信息
    # 提示用户可以通过按键1-4切换不同的模式，按下ESC键退出程序
    mode_text = f"Mode: {mode} | 1:Normal 2:Mosaic 3:Canny 4:HSV | ESC"
    # cv2.putText用于在图像上绘制文本，参数依次为图像、文本内容、文本位置、字体、字体大小、颜色和线条粗细
    cv2.putText(frame, mode_text, (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

    # 显示处理后的帧
    # cv2.imshow用于显示图像，参数依次为窗口名称和要显示的图像
    cv2.imshow("Face Processing System", frame)

    # 等待按键事件，等待时间为1毫秒
    # cv2.waitKey返回按键的ASCII码值
    key = cv2.waitKey(1)
    # 如果按下按键1，将模式设置为普通检测模式
    if key == ord('1'):
        mode = 1
    # 如果按下按键2，将模式设置为马赛克模式
    elif key == ord('2'):
        mode = 2
    # 如果按下按键3，将模式设置为边缘检测模式
    elif key == ord('3'):
        mode = 3
    # 如果按下按键4，将模式设置为HSV色彩空间变换模式
    elif key == ord('4'):
        mode = 4
    # 如果按下ESC键（ASCII码值为27），跳出循环，结束程序
    elif key == 27:
        break

# 释放摄像头资源
# cap.release()用于释放摄像头设备，以便其他程序可以使用
cap.release()
# 关闭所有由OpenCV创建的窗口
# cv2.destroyAllWindows()用于关闭所有打开的窗口
cv2.destroyAllWindows()